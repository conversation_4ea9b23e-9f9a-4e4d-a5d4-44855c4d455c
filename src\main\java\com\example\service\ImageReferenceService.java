package com.example.service;

import com.example.entity.Topology;
import com.example.entity.BiWidget;
import com.example.entity.BiDashboard;
import com.example.model.Device;
import com.example.repository.DeviceRepository;
import com.example.repository.TopologyRepository;
import com.example.repository.BiWidgetRepository;
import com.example.repository.BiDashboardRepository;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ImageReferenceService {
    private static final Logger logger = LoggerFactory.getLogger(ImageReferenceService.class);

    private final DeviceRepository deviceRepository;
    private final TopologyRepository topologyRepository;
    private final BiWidgetRepository biWidgetRepository;
    private final BiDashboardRepository biDashboardRepository;
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Value("${server.external-url}")
    private String serverExternalUrl;
    
    @Value("${upload.image.url-prefix}")
    private String urlPrefix;
    
    // 系统图片内部类定义
    private static class SystemImage {
        private final String filename;
        private final String page;
        private final String description;

        public SystemImage(String filename, String page, String description) {
            this.filename = filename;
            this.page = page;
            this.description = description;
        }
    }

    // 初始化系统图片列表
    private final List<SystemImage> systemImages = Arrays.asList(
        new SystemImage("logo.png", "主页", "系统LOGO"),
        new SystemImage("tech-bg.jpg", "登录页", "登录页背景图")
    );

    @Autowired
    public ImageReferenceService(DeviceRepository deviceRepository, TopologyRepository topologyRepository,
                               BiWidgetRepository biWidgetRepository, BiDashboardRepository biDashboardRepository) {
        this.deviceRepository = deviceRepository;
        this.topologyRepository = topologyRepository;
        this.biWidgetRepository = biWidgetRepository;
        this.biDashboardRepository = biDashboardRepository;
    }

    /**
     * 检查图片是否被引用并返回引用信息
     * @param fileName 图片文件名
     * @return 包含使用状态和引用列表的Map
     */
    public Map<String, Object> checkImageUsage(String fileName) {
        Map<String, Object> result = new HashMap<>();
        boolean isUsed = false;
        List<Map<String, String>> references = new ArrayList<>();

        try {
            // 1. 首先检查是否为系统图片
            for (SystemImage sysImg : systemImages) {
                if (sysImg.filename.equals(fileName)) {
                    isUsed = true;
                    Map<String, String> reference = new HashMap<>();
                    reference.put("type", "system");
                    reference.put("page", sysImg.page);
                    reference.put("description", sysImg.description);
                    references.add(reference);
                    // 不要 break，允许检测同一个图片可能在多个系统页面使用
                }
            }
            
            // 2. 继续检查设备引用
            // 构建可能的图片URL（用于和设备中保存的URL进行比较）
            String possibleImageUrl = "http://" + serverExternalUrl + urlPrefix + "/" + fileName;
            
            // 检查设备引用
            List<Device> devices = deviceRepository.findAll();
            for (Device device : devices) {
                if (device.getImageUrl() != null && 
                    (device.getImageUrl().equals(possibleImageUrl) || 
                     device.getImageUrl().endsWith("/" + fileName))) {
                    isUsed = true;
                    Map<String, String> reference = new HashMap<>();
                    reference.put("type", "device");
                    reference.put("id", device.getId());
                    reference.put("name", device.getName());
                    references.add(reference);
                }
            }

            // 3. 继续检查拓扑引用
            List<Topology> topologies = topologyRepository.findAll();
            for (Topology topology : topologies) {
                String data = topology.getData();
                if (data != null && !data.isEmpty()) {
                    try {
                        // 简单检查：如果JSON字符串中包含文件名，再进行详细解析
                        if (data.contains(fileName)) {
                            JsonNode jsonNode = objectMapper.readTree(data);
                            
                            // 检查设备节点
                            if (jsonNode.has("nodes")) {
                                JsonNode nodes = jsonNode.get("nodes");
                                for (JsonNode node : nodes) {
                                    if (node.has("imageUrl") && 
                                        node.get("imageUrl").asText().contains(fileName)) {
                                        isUsed = true;
                                        Map<String, String> reference = new HashMap<>();
                                        reference.put("type", "topology_device");
                                        reference.put("topology", topology.getName());
                                        reference.put("nodeName", node.has("name") ? 
                                                               node.get("name").asText() : "未命名节点");
                                        references.add(reference);
                                    }
                                }
                            }
                            
                            // 检查图片节点
                            if (jsonNode.has("imageNodes")) {
                                JsonNode imageNodes = jsonNode.get("imageNodes");
                                for (JsonNode node : imageNodes) {
                                    if (node.has("imageUrl") && 
                                        node.get("imageUrl").asText().contains(fileName)) {
                                        isUsed = true;
                                        Map<String, String> reference = new HashMap<>();
                                        reference.put("type", "topology_image");
                                        reference.put("topology", topology.getName());
                                        reference.put("title", node.has("title") ? 
                                                           node.get("title").asText() : "未命名图片");
                                        references.add(reference);
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        logger.error("解析拓扑JSON数据失败: {}", e.getMessage());
                        // 解析JSON失败，继续检查下一个拓扑
                        continue;
                    }
                }
            }

            // 4. 检查BI仪表盘图片组件引用
            List<BiWidget> biWidgets = biWidgetRepository.findAll();
            for (BiWidget widget : biWidgets) {
                if ("image-widget".equals(widget.getWidgetType()) && widget.getConfig() != null) {
                    try {
                        JsonNode configNode = objectMapper.readTree(widget.getConfig());
                        if (configNode.has("imageUrl")) {
                            String imageUrl = configNode.get("imageUrl").asText();
                            if (imageUrl.contains(fileName) || imageUrl.equals(possibleImageUrl)) {
                                isUsed = true;
                                Map<String, String> reference = new HashMap<>();
                                reference.put("type", "bi_dashboard");
                                reference.put("dashboardId", widget.getDashboardId().toString());
                                reference.put("widgetId", widget.getId().toString());
                                reference.put("description", "BI仪表盘图片组件");
                                references.add(reference);
                            }
                        }
                    } catch (Exception e) {
                        logger.error("解析BI组件配置失败: {}", e.getMessage());
                        // 继续检查下一个组件
                        continue;
                    }
                }
            }

            // 5. 检查BI仪表盘画布背景图片引用
            List<BiDashboard> biDashboards = biDashboardRepository.findAll();
            for (BiDashboard dashboard : biDashboards) {
                if (dashboard.getCanvasConfig() != null && !dashboard.getCanvasConfig().isEmpty()) {
                    try {
                        JsonNode canvasConfigNode = objectMapper.readTree(dashboard.getCanvasConfig());

                        // 只有当背景类型为图片时，才检查背景图片的使用状态
                        if (canvasConfigNode.has("backgroundType") && canvasConfigNode.has("backgroundImage")) {
                            String backgroundType = canvasConfigNode.get("backgroundType").asText();
                            String backgroundImageUrl = canvasConfigNode.get("backgroundImage").asText();

                            // 只有背景类型为'image'且背景图片URL不为空时，才认为图片被使用
                            if ("image".equals(backgroundType) &&
                                backgroundImageUrl != null && !backgroundImageUrl.isEmpty() &&
                                (backgroundImageUrl.contains(fileName) || backgroundImageUrl.equals(possibleImageUrl))) {
                                isUsed = true;
                                Map<String, String> reference = new HashMap<>();
                                reference.put("type", "bi_dashboard_background");
                                reference.put("dashboardId", dashboard.getId().toString());
                                reference.put("dashboardName", dashboard.getName());
                                reference.put("description", "BI仪表盘画布背景图片");
                                references.add(reference);
                            }
                        }
                    } catch (Exception e) {
                        logger.error("解析BI仪表盘画布配置失败: {}", e.getMessage());
                        // 继续检查下一个仪表盘
                        continue;
                    }
                }
            }
        } catch (Exception e) {
            logger.error("检查图片使用状态时出错: {}", e.getMessage());
        }

        result.put("isUsed", isUsed);
        result.put("references", references);
        return result;
    }
} 