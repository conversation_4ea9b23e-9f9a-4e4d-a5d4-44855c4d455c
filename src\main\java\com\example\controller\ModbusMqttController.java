package com.example.controller;

import com.example.model.Device;
import com.example.model.DataItem;
import com.example.model.ErrorResponse;
import com.example.service.DeviceService;
import com.example.service.ModbusService;
import com.example.service.MqttService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api")
public class ModbusMqttController {
    private final ModbusService modbusService;
    private final MqttService mqttService;
    private final DeviceService deviceService;

    @Autowired
    public ModbusMqttController(ModbusService modbusService, MqttService mqttService, DeviceService deviceService) {
        this.modbusService = modbusService;
        this.mqttService = mqttService;
        this.deviceService = deviceService;
    }

    // Device Management APIs
    @PostMapping("/device")
    public ResponseEntity<?> addDevice(@RequestBody Map<String, Object> params) {
        try {
            String id = (String) params.get("id");
            String name = (String) params.get("name");
            String address = (String) params.get("address");
            Integer port = (Integer) params.get("port");
            
            if (id == null || name == null || address == null || port == null) {
                return ResponseEntity.badRequest()
                    .body(new ErrorResponse("参数错误", "设备ID、名称、地址和端口号不能为空"));
            }
            
            Device device = deviceService.addDevice(id, name, address, port);
            return ResponseEntity.ok(device);
        } catch (Exception e) {
            log.error("Error adding device", e);
            return ResponseEntity.badRequest()
                .body(new ErrorResponse("添加设备失败", e.getMessage()));
        }
    }

    @DeleteMapping("/device/{id}")
    public ResponseEntity<?> deleteDevice(@PathVariable String id) {
        try {
            deviceService.deleteDevice(id);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("Error deleting device", e);
            return ResponseEntity.badRequest()
                .body(new ErrorResponse("删除设备失败", e.getMessage()));
        }
    }

    @PutMapping("/device/{id}")
    public ResponseEntity<?> updateDevice(@PathVariable String id, @RequestBody Map<String, Object> params) {
        try {
            String name = (String) params.get("name");
            String address = (String) params.get("address");
            Integer port = (Integer) params.get("port");

            if (name == null || address == null || port == null) {
                return ResponseEntity.badRequest()
                    .body(new ErrorResponse("参数错误", "设备名称、地址和端口号不能为空"));
            }

            Device device = deviceService.updateDevice(id, name, address, port);
            return ResponseEntity.ok(device);
        } catch (Exception e) {
            log.error("Error updating device", e);
            return ResponseEntity.badRequest()
                .body(new ErrorResponse("修改设备失败", e.getMessage()));
        }
    }

    @PutMapping("/device/{id}/image")
    public ResponseEntity<?> updateDeviceImage(@PathVariable String id, @RequestBody Map<String, String> params) {
        try {
            String imageUrl = params.get("imageUrl");
            if (imageUrl == null) {
                return ResponseEntity.badRequest()
                    .body(new ErrorResponse("参数错误", "图片URL参数不能为null"));
            }

            // 允许空字符串，用于移除图片
            Device device = deviceService.updateDeviceImage(id, imageUrl.trim());
            return ResponseEntity.ok(device);
        } catch (Exception e) {
            log.error("Error updating device image", e);
            return ResponseEntity.badRequest()
                .body(new ErrorResponse("更新设备图片失败", e.getMessage()));
        }
    }

    @GetMapping("/devices")
    public ResponseEntity<List<Device>> getAllDevices() {
        return ResponseEntity.ok(deviceService.getAllDevices());
    }

    @GetMapping("/device/{id}")
    public ResponseEntity<?> getDevice(@PathVariable String id) {
        try {
            Device device = deviceService.getDevice(id)
                .orElseThrow(() -> new IllegalArgumentException("设备不存在: " + id));
            return ResponseEntity.ok(device);
        } catch (Exception e) {
            log.error("Error getting device", e);
            return ResponseEntity.badRequest()
                .body(new ErrorResponse("获取设备失败", e.getMessage()));
        }
    }

    // Data Item Management APIs
    @PostMapping("/device/{deviceId}/data-item")
    public ResponseEntity<?> addDataItem(@PathVariable String deviceId, @RequestBody Map<String, Object> params) {
        try {
            String itemId = (String) params.get("id");
            String name = (String) params.get("name");
            String address = (String) params.get("address");
            Integer refreshInterval = (Integer) params.get("refreshInterval");
            
            if (itemId == null || name == null || address == null || refreshInterval == null) {
                return ResponseEntity.badRequest()
                    .body(new ErrorResponse("参数错误", "数据项ID、名称、地址和刷新间隔不能为空"));
            }
            
            DataItem dataItem = deviceService.addDataItem(deviceId, itemId, name, address, refreshInterval);
            return ResponseEntity.ok(dataItem);
        } catch (Exception e) {
            log.error("Error adding data item", e);
            return ResponseEntity.badRequest()
                .body(new ErrorResponse("添加数据项失败", e.getMessage()));
        }
    }

    @DeleteMapping("/data-item/{id}")
    public ResponseEntity<?> deleteDataItem(@PathVariable String id) {
        try {
            deviceService.deleteDataItem(id);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("Error deleting data item", e);
            return ResponseEntity.badRequest()
                .body(new ErrorResponse("删除数据项失败", e.getMessage()));
        }
    }

    @GetMapping("/device/{deviceId}/data-items")
    public ResponseEntity<?> getDeviceDataItems(@PathVariable String deviceId) {
        try {
            List<DataItem> items = deviceService.getDeviceDataItems(deviceId);
            List<Map<String, Object>> result = items.stream()
                .map(item -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("id", item.getId());
                    map.put("name", item.getName());
                    map.put("address", item.getAddress());
                    map.put("refreshInterval", item.getRefreshInterval());
                    map.put("deviceId", item.getDevice().getId());
                    map.put("historyEnabled", item.getHistoryEnabled());
                    map.put("historyRetentionHours", item.getHistoryRetentionHours());
                    return map;
                })
                .collect(Collectors.toList());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Error getting device data items", e);
            return ResponseEntity.badRequest()
                .body(new ErrorResponse("获取数据项失败", e.getMessage()));
        }
    }

    @PutMapping("/data-item/{id}")
    public ResponseEntity<?> updateDataItem(@PathVariable String id, @RequestBody Map<String, Object> params) {
        try {
            log.info("更新监控项请求 - ID: {}, 参数: {}", id, params);
            
            String name = (String) params.get("name");
            Integer refreshInterval = (Integer) params.get("refreshInterval");
            
            // 参数验证
            if (name == null || name.trim().isEmpty()) {
                log.warn("更新监控项失败 - 名称为空 - ID: {}", id);
                return ResponseEntity.badRequest()
                    .body(new ErrorResponse("参数错误", "监控项名称不能为空"));
            }
            
            if (refreshInterval == null || refreshInterval < 100) {
                log.warn("更新监控项失败 - 更新间隔无效 - ID: {}, 间隔: {}", id, refreshInterval);
                return ResponseEntity.badRequest()
                    .body(new ErrorResponse("参数错误", "更新间隔不能小于100毫秒"));
            }
            
            DataItem dataItem = deviceService.updateDataItem(id, name, refreshInterval);
            log.info("更新监控项成功 - ID: {}", id);
            
            // 构造简化的响应数据
            Map<String, Object> result = new HashMap<>();
            result.put("id", dataItem.getId());
            result.put("name", dataItem.getName());
            result.put("address", dataItem.getAddress());
            result.put("refreshInterval", dataItem.getRefreshInterval());
            result.put("deviceId", dataItem.getDevice().getId());
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("更新监控项失败 - ID: {} - 错误: {}", id, e.getMessage(), e);
            String message = e.getMessage();
            // 处理常见错误类型
            if (message.contains("监控项不存在")) {
                return ResponseEntity.status(404)
                    .body(new ErrorResponse("更新失败", "监控项不存在"));
            }
            return ResponseEntity.badRequest()
                .body(new ErrorResponse("更新失败", message));
        }
    }

    @PostMapping("/modbus/connect")
    public ResponseEntity<?> connectModbus(@RequestBody Map<String, Object> params) {
        try {
            String deviceId = (String) params.get("deviceId");
            String host = (String) params.get("host");
            Integer port = (Integer) params.get("port");
            
            if (deviceId == null || host == null || port == null) {
                return ResponseEntity.badRequest()
                    .body(new ErrorResponse("参数错误", "设备ID、IP地址和端口号不能为空"));
            }
            
            modbusService.connect(deviceId, host, port);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("Error connecting to Modbus device", e);
            return ResponseEntity.badRequest()
                .body(new ErrorResponse("连接错误", e.getMessage()));
        }
    }

    @PostMapping("/modbus/disconnect")
    public ResponseEntity<?> disconnectModbus(@RequestBody Map<String, String> params) {
        try {
            String deviceId = params.get("deviceId");
            if (deviceId == null) {
                return ResponseEntity.badRequest()
                    .body(new ErrorResponse("参数错误", "设备ID不能为空"));
            }
            
            modbusService.disconnect(deviceId);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("Error disconnecting from Modbus device", e);
            return ResponseEntity.badRequest()
                .body(new ErrorResponse("断开连接错误", e.getMessage()));
        }
    }

    @GetMapping("/modbus/read")
    public ResponseEntity<?> readModbusRegister(
            @RequestParam String deviceId,
            @RequestParam String address) {
        try {
            if (deviceId == null || address == null || address.trim().isEmpty()) {
                return ResponseEntity.badRequest()
                    .body(new ErrorResponse("参数错误", "设备ID和寄存器地址不能为空"));
            }

            Map<String, Object> result = modbusService.readRegister(deviceId, address);
            return ResponseEntity.ok(result);
        } catch (IllegalArgumentException e) {
            log.error("Invalid register address", e);
            return ResponseEntity.badRequest()
                .body(new ErrorResponse("参数错误", e.getMessage()));
        } catch (Exception e) {
            log.error("Error reading Modbus register", e);
            return ResponseEntity.badRequest()
                .body(new ErrorResponse("读取错误", e.getMessage()));
        }
    }

    @PostMapping("/modbus/write")
    public ResponseEntity<?> writeModbusRegisters(
            @RequestParam String deviceId,
            @RequestParam String address,
            @RequestBody int[] values) {
        try {
            if (deviceId == null || address == null || address.trim().isEmpty()) {
                return ResponseEntity.badRequest()
                    .body(new ErrorResponse("参数错误", "设备ID和寄存器地址不能为空"));
            }
            
            modbusService.writeRegister(deviceId, address, values);
            return ResponseEntity.ok().build();
        } catch (IllegalArgumentException e) {
            log.error("Invalid register address or values", e);
            return ResponseEntity.badRequest()
                .body(new ErrorResponse("参数错误", e.getMessage()));
        } catch (Exception e) {
            log.error("Error writing Modbus registers", e);
            return ResponseEntity.badRequest()
                .body(new ErrorResponse("写入错误", e.getMessage()));
        }
    }

    @PostMapping("/mqtt/connect")
    public ResponseEntity<?> connectMqtt(@RequestParam String brokerUrl) {
        try {
            mqttService.connect(brokerUrl);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("Error connecting to MQTT broker", e);
            return ResponseEntity.badRequest()
                .body(new ErrorResponse("连接错误", e.getMessage()));
        }
    }

    @PostMapping("/mqtt/publish")
    public ResponseEntity<?> publishMqtt(
            @RequestParam String topic,
            @RequestParam String message) {
        try {
            mqttService.publish(topic, message);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("Error publishing MQTT message", e);
            return ResponseEntity.badRequest()
                .body(new ErrorResponse("发布错误", e.getMessage()));
        }
    }

    @GetMapping("/modbus/batch-read")
    public ResponseEntity<?> batchReadRegisters(
            @RequestParam String deviceId,
            @RequestParam String address,
            @RequestParam int count) {
        try {
            if (deviceId == null || address == null || address.trim().isEmpty()) {
                return ResponseEntity.badRequest()
                    .body(new ErrorResponse("参数错误", "设备ID和起始地址不能为空"));
            }

            int[] values = modbusService.batchReadRegisters(deviceId, address, count);
            Map<String, Object> result = new HashMap<>();
            result.put("deviceId", deviceId);
            result.put("startAddress", address);
            result.put("count", count);
            result.put("values", values);
            return ResponseEntity.ok(result);
        } catch (IllegalArgumentException e) {
            log.error("Invalid register address", e);
            return ResponseEntity.badRequest()
                .body(new ErrorResponse("参数错误", e.getMessage()));
        } catch (Exception e) {
            log.error("Error batch reading Modbus registers", e);
            return ResponseEntity.badRequest()
                .body(new ErrorResponse("读取错误", e.getMessage()));
        }
    }

    @GetMapping("/devices/connected")
    public ResponseEntity<?> getConnectedDevices() {
        try {
            List<Device> connectedDevices = deviceService.findConnectedDevices();
            return ResponseEntity.ok(connectedDevices);
        } catch (Exception e) {
            log.error("获取已连接设备列表失败", e);
            return ResponseEntity.badRequest().body(new ErrorResponse("获取已连接设备列表失败: " + e.getMessage()));
        }
    }

    @PostMapping("/data-item/{id}/write")
    public ResponseEntity<?> writeDataItemValue(@PathVariable String id, @RequestBody Map<String, Object> params) {
        try {
            Integer value = (Integer) params.get("value");
            if (value == null) {
                return ResponseEntity.badRequest()
                    .body(new ErrorResponse("参数错误", "写入值不能为空"));
            }

            if (value < -32768 || value > 32767) {
                return ResponseEntity.badRequest()
                    .body(new ErrorResponse("参数错误", "写入值必须在-32768到32767之间"));
            }

            // 获取数据项信息
            DataItem dataItem = deviceService.getDataItem(id)
                .orElseThrow(() -> new IllegalArgumentException("数据项不存在: " + id));

            // 写入到Modbus设备
            modbusService.writeRegister(dataItem.getDevice().getId(),
                dataItem.getAddress(), new int[]{value});

            // 更新数据项的最新值
            dataItem.setLatestValue(value);
            deviceService.saveDataItem(dataItem);

            log.info("数据项写入成功 - ID: {}, 地址: {}, 值: {}", id, dataItem.getAddress(), value);
            return ResponseEntity.ok().build();
        } catch (IllegalArgumentException e) {
            log.error("数据项写入失败 - 参数错误: {}", e.getMessage());
            return ResponseEntity.badRequest()
                .body(new ErrorResponse("参数错误", e.getMessage()));
        } catch (Exception e) {
            log.error("数据项写入失败 - ID: {} - 错误: {}", id, e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(new ErrorResponse("写入失败", e.getMessage()));
        }
    }
}