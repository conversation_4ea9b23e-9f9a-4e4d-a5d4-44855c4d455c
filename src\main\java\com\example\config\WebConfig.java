package com.example.config;

import com.example.interceptor.AuthInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.nio.file.Path;
import java.nio.file.Paths;

@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Autowired
    private AuthInterceptor authInterceptor;

    @Value("${upload.image.path}")
    private String uploadPath;

    @Value("${upload.material.path}")
    private String materialUploadPath;

    @Value("${upload.video.path}")
    private String videoUploadPath;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(authInterceptor)
               .addPathPatterns("/**");
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 处理上传路径
        Path uploadDir;
        if (Paths.get(uploadPath).isAbsolute()) {
            // 如果是绝对路径，直接使用
            uploadDir = Paths.get(uploadPath).normalize();
        } else {
            // 如果是相对路径，使用项目根目录 + 相对路径
            String userDir = System.getProperty("user.dir");
            uploadDir = Paths.get(userDir, uploadPath).toAbsolutePath().normalize();
        }
        String uploadAbsolutePath = uploadDir.toString();

        // 添加图片资源处理器，将/images/**的请求映射到实际的文件目录
        registry.addResourceHandler("/images/**")
                .addResourceLocations("file:" + uploadAbsolutePath + "/")
                .setCachePeriod(3600) // 缓存1小时
                .resourceChain(true); // 开启资源链优化

        // 处理素材上传路径
        Path materialUploadDir;
        if (Paths.get(materialUploadPath).isAbsolute()) {
            // 如果是绝对路径，直接使用
            materialUploadDir = Paths.get(materialUploadPath).normalize();
        } else {
            // 如果是相对路径，使用项目根目录 + 相对路径
            String userDir = System.getProperty("user.dir");
            materialUploadDir = Paths.get(userDir, materialUploadPath).toAbsolutePath().normalize();
        }
        String materialUploadAbsolutePath = materialUploadDir.toString();

        // 添加素材资源处理器，将/materials/**的请求映射到实际的素材文件目录
        registry.addResourceHandler("/materials/**")
                .addResourceLocations("file:" + materialUploadAbsolutePath + "/")
                .setCachePeriod(3600) // 缓存1小时
                .resourceChain(true); // 开启资源链优化

        // 处理视频上传路径
        Path videoUploadDir;
        if (Paths.get(videoUploadPath).isAbsolute()) {
            // 如果是绝对路径，直接使用
            videoUploadDir = Paths.get(videoUploadPath).normalize();
        } else {
            // 如果是相对路径，使用项目根目录 + 相对路径
            String userDir = System.getProperty("user.dir");
            videoUploadDir = Paths.get(userDir, videoUploadPath).toAbsolutePath().normalize();
        }
        String videoUploadAbsolutePath = videoUploadDir.toString();

        // 添加视频资源处理器，将/videos/**的请求映射到实际的视频文件目录
        registry.addResourceHandler("/videos/**")
                .addResourceLocations("file:" + videoUploadAbsolutePath + "/")
                .setCachePeriod(3600) // 缓存1小时
                .resourceChain(true); // 开启资源链优化
    }
} 