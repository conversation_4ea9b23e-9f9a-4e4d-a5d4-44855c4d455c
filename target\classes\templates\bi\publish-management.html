<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>胜大科技智联管理系统 - BI大屏发布管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" th:href="@{/css/button-styles.css}">
    <style>
        .status-badge {
            font-size: 0.8em;
        }
        .access-url {
            font-family: monospace;
            font-size: 0.85em;
            background-color: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .url-text {
            color: #0066cc;
            text-decoration: none;
        }
        .copy-btn {
            font-size: 0.8em;
            padding: 4px 8px;
            white-space: nowrap;
        }
        .copy-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        .publish-form {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <!-- 导航条 -->
    <div th:replace="fragments/navbar :: navbar('胜大科技智联管理系统 - BI大屏发布管理', 'biPublish', true, true, true, null)"></div>

    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="bi bi-share"></i> BI大屏发布管理</h2>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#publishModal">
                        <i class="bi bi-plus-circle"></i> 发布新大屏
                    </button>
                </div>

                <!-- 发布统计 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-primary" th:text="${#lists.size(publishedList)}">0</h5>
                                <p class="card-text">总发布数</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-success">
                                    <span th:text="${#lists.size(publishedList)}">0</span>
                                </h5>
                                <p class="card-text">有效发布</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-warning">0</h5>
                                <p class="card-text">已过期</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-danger">0</h5>
                                <p class="card-text">已撤销</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 发布列表 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-list"></i> 发布列表</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>发布名称</th>
                                        <th>大屏名称</th>
                                        <th>访问地址</th>
                                        <th>发布时间</th>
                                        <th>有效期</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="published : ${publishedList}">
                                        <td th:text="${published.name}">发布名称</td>
                                        <td th:text="${published.dashboardName}">大屏名称</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <span class="access-url flex-grow-1" th:data-token="${published.accessToken}">
                                                    <span class="url-text"></span>
                                                </span>
                                                <button class="btn btn-success btn-sm copy-btn ms-2"
                                                        th:data-token="${published.accessToken}"
                                                        onclick="copyAccessUrl(this.dataset.token)"
                                                        title="复制访问地址">
                                                    <i class="bi bi-clipboard-check me-1"></i>复制
                                                </button>
                                            </div>
                                        </td>
                                        <td th:text="${#dates.format(published.publishedAt, 'yyyy-MM-dd HH:mm')}">发布时间</td>
                                        <td>
                                            <span th:if="${published.expiryDate == null}" class="badge bg-info">永久</span>
                                            <span th:if="${published.expiryDate != null}"
                                                  th:text="${#dates.format(published.expiryDate, 'yyyy-MM-dd HH:mm')}"
                                                  class="badge bg-success">
                                                过期时间
                                            </span>
                                        </td>
                                        <td>
                                            <span th:if="${published.status == 'ACTIVE'}" class="badge bg-success status-badge">有效</span>
                                            <span th:if="${published.status == 'EXPIRED'}" class="badge bg-warning status-badge">已过期</span>
                                            <span th:if="${published.status == 'REVOKED'}" class="badge bg-danger status-badge">已撤销</span>
                                        </td>
                                        <td>
                                            <!-- 查看按钮 - 所有状态都可以查看 -->
                                            <a th:href="@{'/bi/published/' + ${published.accessToken}}"
                                               class="btn btn-outline-primary btn-sm me-1" target="_blank">
                                                <i class="bi bi-eye"></i> 查看
                                            </a>

                                            <!-- ACTIVE状态：显示撤销按钮 -->
                                            <button th:if="${published.status == 'ACTIVE'}"
                                                    class="btn btn-outline-warning btn-sm me-1"
                                                    th:data-id="${published.id}"
                                                    onclick="revokePublish(this.dataset.id)"
                                                    title="撤销发布">
                                                <i class="bi bi-x-circle"></i> 撤销
                                            </button>

                                            <!-- REVOKED状态：显示恢复按钮 -->
                                            <button th:if="${published.status == 'REVOKED'}"
                                                    class="btn btn-outline-success btn-sm me-1"
                                                    th:data-id="${published.id}"
                                                    onclick="restorePublish(this.dataset.id)"
                                                    title="恢复发布">
                                                <i class="bi bi-arrow-clockwise"></i> 恢复
                                            </button>

                                            <!-- 删除按钮 - 所有状态都可以删除 -->
                                            <button class="btn btn-outline-danger btn-sm"
                                                    th:data-id="${published.id}"
                                                    onclick="deletePublish(this.dataset.id)"
                                                    title="删除记录">
                                                <i class="bi bi-trash"></i> 删除
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div th:if="${#lists.isEmpty(publishedList)}" class="text-center py-4">
                            <i class="bi bi-inbox display-4 text-muted"></i>
                            <p class="text-muted mt-2">暂无发布记录</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 发布模态框 -->
    <div class="modal fade" id="publishModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-share"></i> 发布大屏</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="publishForm">
                        <div class="mb-3">
                            <label for="dashboardSelect" class="form-label">选择大屏 <span class="text-danger">*</span></label>
                            <select class="form-select" id="dashboardSelect" name="dashboardId" required>
                                <option value="">请选择要发布的大屏</option>
                                <option th:each="dashboard : ${dashboards}"
                                        th:value="${dashboard.id}"
                                        th:text="${dashboard.name}">大屏名称</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="publishName" class="form-label">发布名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="publishName" name="name"
                                   placeholder="输入发布名称" required>
                        </div>

                        <div class="mb-3">
                            <label for="publishDescription" class="form-label">发布描述</label>
                            <textarea class="form-control" id="publishDescription" name="description"
                                      rows="3" placeholder="输入发布描述（可选）"></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="validitySelect" class="form-label">有效期 <span class="text-danger">*</span></label>
                            <select class="form-select" id="validitySelect" name="validityDays" required>
                                <option th:each="option : ${validityOptions}"
                                        th:value="${option.value}"
                                        th:text="${option.key}">选项</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="submitPublish()">
                        <i class="bi bi-share"></i> 发布
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 提交发布
        function submitPublish() {
            const form = document.getElementById('publishForm');
            const formData = new FormData(form);

            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            fetch('/bi/published/publish', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('发布成功！\n访问令牌：' + data.accessToken);
                    location.reload();
                } else {
                    alert('发布失败：' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('发布失败：网络错误');
            });
        }

        // 撤销发布
        function revokePublish(publishedId) {
            if (!confirm('确定要撤销这个发布吗？撤销后将无法通过访问令牌访问。')) {
                return;
            }

            fetch('/bi/published/revoke/' + publishedId, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('撤销成功！', 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showToast('撤销失败：' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('撤销失败：网络错误', 'error');
            });
        }

        // 恢复发布
        function restorePublish(publishedId) {
            if (!confirm('确定要恢复这个发布吗？恢复后将可以通过访问令牌重新访问。')) {
                return;
            }

            fetch('/bi/published/restore/' + publishedId, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('恢复成功！', 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showToast('恢复失败：' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('恢复失败：网络错误', 'error');
            });
        }

        // 删除发布记录
        function deletePublish(publishedId) {
            if (!confirm('确定要删除这个发布记录吗？\n\n⚠️ 警告：删除后将无法恢复，所有相关数据将被永久删除！')) {
                return;
            }

            // 二次确认
            if (!confirm('请再次确认：您真的要永久删除这个发布记录吗？')) {
                return;
            }

            fetch('/bi/published/delete/' + publishedId, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('删除成功！', 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showToast('删除失败：' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('删除失败：网络错误', 'error');
            });
        }

        // 显示提示消息
        function showToast(message, type = 'info') {
            const bgClass = type === 'success' ? 'bg-success' :
                           type === 'error' ? 'bg-danger' : 'bg-info';
            const icon = type === 'success' ? 'bi-check-circle' :
                        type === 'error' ? 'bi-exclamation-triangle' : 'bi-info-circle';

            const toast = document.createElement('div');
            toast.className = 'toast align-items-center text-white border-0 position-fixed ' + bgClass;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999;';
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="bi ${icon} me-2"></i>${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;

            document.body.appendChild(toast);
            const bsToast = new bootstrap.Toast(toast, { delay: 3000 });
            bsToast.show();

            toast.addEventListener('hidden.bs.toast', () => {
                document.body.removeChild(toast);
            });
        }

        // 复制访问地址到剪贴板
        function copyAccessUrl(token) {
            const baseUrl = window.location.origin;
            const fullUrl = `${baseUrl}/bi/published/${token}`;

            navigator.clipboard.writeText(fullUrl).then(function() {
                showToast('访问地址已复制到剪贴板', 'success');
            }).catch(function(err) {
                console.error('复制失败:', err);
                showToast('复制失败，请手动复制', 'error');
            });
        }

        // 大屏选择变化时自动填充发布名称
        document.getElementById('dashboardSelect').addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            if (selectedOption.value) {
                const dashboardName = selectedOption.text;
                document.getElementById('publishName').value = dashboardName + ' - 发布版';
            }
        });

        // 页面加载完成后生成访问地址
        document.addEventListener('DOMContentLoaded', function() {
            const baseUrl = window.location.origin;
            const accessUrlElements = document.querySelectorAll('.access-url');

            accessUrlElements.forEach(function(element) {
                const token = element.getAttribute('data-token');
                if (token) {
                    const fullUrl = `${baseUrl}/bi/published/${token}`;
                    const urlTextElement = element.querySelector('.url-text');
                    if (urlTextElement) {
                        urlTextElement.textContent = fullUrl;
                        urlTextElement.title = fullUrl; // 添加完整URL的提示
                    }
                }
            });
        });
    </script>
