package com.example.interceptor;

import com.example.license.LicenseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.servlet.HandlerInterceptor;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.Arrays;
import java.util.List;

@Component
public class AuthInterceptor implements HandlerInterceptor {

    @Autowired
    private LicenseService licenseService;

    private static final List<String> WHITELIST = Arrays.asList(
        "/auth/login",
        "/license/**",
        "/css/**",
        "/js/**",
        "/fonts/**",
        "/images/**",
        "/materials/**",
        "/videos/**",
        "/published/**",
        "/upload/images/**",
        "/api/devices",
        "/api/device/**",
        "/api/alerts/**",
        "/api/materials/**"
    );
    
    @Override
    public boolean preHandle(HttpServletRequest request,
                           HttpServletResponse response,
                           Object handler) throws Exception {
        String path = request.getRequestURI();

        System.out.println("=== AuthInterceptor 拦截请求 ===");
        System.out.println("请求路径: " + path);

        // 如果是白名单路径，直接放行
        if (isWhitelisted(path)) {
            System.out.println("白名单路径，直接放行");
            return true;
        }

        // 首先检查许可证是否有效
        System.out.println("检查许可证状态...");
        boolean licenseValid = licenseService != null && licenseService.isLicenseValid();
        System.out.println("许可证有效: " + licenseValid);

        if (!licenseValid) {
            System.out.println("许可证无效，重定向到激活页面");
            response.sendRedirect("/license/activation");
            return false;
        }

        // 检查用户是否已登录
        HttpSession session = request.getSession();
        boolean userLoggedIn = session.getAttribute("user") != null;
        System.out.println("用户已登录: " + userLoggedIn);

        if (!userLoggedIn) {
            System.out.println("用户未登录，重定向到登录页面");
            response.sendRedirect("/auth/login");
            return false;
        }

        System.out.println("所有检查通过，允许访问");
        return true;
    }
    
    private boolean isWhitelisted(String path) {
        // 特殊处理：允许公开访问BI发布页面（通过访问令牌），但不允许访问管理页面
        if (path.startsWith("/bi/published/")) {
            // 只允许访问令牌页面（格式：/bi/published/{accessToken}），不允许访问管理页面和API
            return !path.equals("/bi/published/management") &&
                   !path.startsWith("/bi/published/publish") &&
                   !path.startsWith("/bi/published/revoke/") &&
                   !path.startsWith("/bi/published/status/") &&
                   path.matches("/bi/published/[a-zA-Z0-9\\-_]+/?");
        }

        return WHITELIST.stream()
                       .anyMatch(pattern ->
                           new AntPathMatcher().match(pattern, path));
    }
} 