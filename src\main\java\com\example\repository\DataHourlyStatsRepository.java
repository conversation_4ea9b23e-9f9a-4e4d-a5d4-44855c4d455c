package com.example.repository;

import com.example.model.DataHourlyStats;
import com.example.model.DataItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface DataHourlyStatsRepository extends JpaRepository<DataHourlyStats, Long> {
    
    List<DataHourlyStats> findByDataItemOrderByHourTimestampDesc(DataItem dataItem);
    
    List<DataHourlyStats> findByDataItemAndHourTimestampBetween(
        DataItem dataItem, 
        LocalDateTime startTime, 
        LocalDateTime endTime
    );
    
    Optional<DataHourlyStats> findByDataItemAndHourTimestamp(
        DataItem dataItem, 
        LocalDateTime hourTimestamp
    );
    
    @Modifying
    @Transactional
    @Query("DELETE FROM DataHourlyStats dhs WHERE dhs.dataItem = :dataItem AND dhs.hourTimestamp < :beforeTime")
    void deleteByDataItemAndHourTimestampBefore(
        @Param("dataItem") DataItem dataItem,
        @Param("beforeTime") LocalDateTime beforeTime
    );
    
    @Query("SELECT dhs FROM DataHourlyStats dhs WHERE dhs.dataItem = :dataItem AND dhs.hourTimestamp = :previousHour")
    Optional<DataHourlyStats> findPreviousHourStats(
        @Param("dataItem") DataItem dataItem,
        @Param("previousHour") LocalDateTime previousHour
    );

    @Modifying
    @Transactional
    @Query("DELETE FROM DataHourlyStats dhs WHERE dhs.dataItem = :dataItem")
    void deleteByDataItem(@Param("dataItem") DataItem dataItem);
}
