package com.example.license;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import java.util.Map;

/**
 * 许可证控制器
 * 处理序列号激活相关的Web请求
 */
@Controller
@RequestMapping("/license")
public class LicenseController {
    
    @Autowired
    private LicenseService licenseService;
    
    /**
     * 显示激活页面
     */
    @GetMapping("/activation")
    public String showActivationPage(Model model) {
        System.out.println("=== 显示激活页面 ===");

        // 检查是否已经激活
        if (licenseService.isLicenseValid()) {
            System.out.println("许可证已有效，重定向到登录页面");
            return "redirect:/auth/login";
        }

        // 获取当前硬件ID用于显示
        String hardwareId = LicenseUtils.generateHardwareId();
        model.addAttribute("hardwareId", hardwareId);
        System.out.println("硬件ID: " + hardwareId);

        return "license/activation";
    }

    /**
     * 显示测试页面
     */
    @GetMapping("/test")
    public String showTestPage(Model model) {
        System.out.println("=== 显示许可证测试页面 ===");

        // 获取当前硬件ID用于显示
        String hardwareId = LicenseUtils.generateHardwareId();
        model.addAttribute("hardwareId", hardwareId);

        // 添加测试数据
        model.addAttribute("testUserId", "USER001");
        model.addAttribute("testSerialNumber", "SDPLC-zymLUJoCsVDDMwEjeZRTPwu++8QYau8JZURuzUAQiarNtwY88fZFKjKf4DzI1Tqas7ILFaaeJFRiVMrxQ6i8ufGgVDk8NPSyh1VVzAFeyRZFjRW9vSEe19S58Z5tgnjP");

        return "license/test";
    }
    
    /**
     * 测试激活请求 - 带详细调试信息
     */
    @PostMapping("/test-activate")
    public String testActivateLicense(@RequestParam(required = false) String userId,
                                     @RequestParam(required = false) String serialNumber,
                                     Model model) {

        StringBuilder debugLog = new StringBuilder();
        debugLog.append("=== 测试激活开始 ===\n");
        debugLog.append("时间: ").append(new java.util.Date()).append("\n");
        debugLog.append("用户ID: ").append(userId).append("\n");
        debugLog.append("序列号: ").append(serialNumber).append("\n");
        debugLog.append("序列号长度: ").append(serialNumber != null ? serialNumber.length() : "null").append("\n");

        try {
            // 步骤1: 验证输入参数
            debugLog.append("\n--- 步骤1: 验证输入参数 ---\n");
            if (userId == null || userId.trim().isEmpty()) {
                debugLog.append("错误: 用户ID为空\n");
                model.addAttribute("debugLog", debugLog.toString());
                model.addAttribute("error", "请输入用户ID");
                return "license/test";
            }
            debugLog.append("用户ID验证通过: ").append(userId.trim()).append("\n");

            if (serialNumber == null || serialNumber.trim().isEmpty()) {
                debugLog.append("错误: 序列号为空\n");
                model.addAttribute("debugLog", debugLog.toString());
                model.addAttribute("error", "请输入序列号");
                return "license/test";
            }
            debugLog.append("序列号验证通过，长度: ").append(serialNumber.trim().length()).append("\n");

            // 步骤2: 获取硬件ID
            debugLog.append("\n--- 步骤2: 获取硬件ID ---\n");
            String hardwareId = LicenseUtils.generateHardwareId();
            debugLog.append("硬件ID: ").append(hardwareId).append("\n");
            model.addAttribute("hardwareId", hardwareId);

            // 步骤3: 调用激活服务
            debugLog.append("\n--- 步骤3: 调用激活服务 ---\n");
            debugLog.append("开始调用 licenseService.activateLicense()\n");
            String result = licenseService.activateLicense(userId.trim(), serialNumber.trim());
            debugLog.append("激活服务返回结果: ").append(result).append("\n");

            // 步骤4: 处理结果
            debugLog.append("\n--- 步骤4: 处理结果 ---\n");
            if ("激活成功".equals(result)) {
                debugLog.append("激活成功！\n");
                model.addAttribute("success", "激活成功！");
            } else {
                debugLog.append("激活失败: ").append(result).append("\n");
                model.addAttribute("error", result);
            }

        } catch (Exception e) {
            debugLog.append("\n--- 异常信息 ---\n");
            debugLog.append("异常类型: ").append(e.getClass().getSimpleName()).append("\n");
            debugLog.append("异常消息: ").append(e.getMessage()).append("\n");
            debugLog.append("堆栈跟踪: \n");
            for (StackTraceElement element : e.getStackTrace()) {
                debugLog.append("  ").append(element.toString()).append("\n");
            }
            model.addAttribute("error", "激活过程中发生异常: " + e.getMessage());
        }

        debugLog.append("\n=== 测试激活结束 ===\n");
        model.addAttribute("debugLog", debugLog.toString());
        model.addAttribute("testUserId", userId);
        model.addAttribute("testSerialNumber", serialNumber);

        System.out.println(debugLog.toString());

        return "license/test";
    }

    /**
     * 处理激活请求
     */
    @PostMapping("/activate")
    public String activateLicense(@RequestParam(required = false) String userId,
                                 @RequestParam(required = false) String serialNumber,
                                 Model model) {



        // 验证输入参数
        if (userId == null || userId.trim().isEmpty()) {
            System.out.println("错误: 用户ID为空");
            model.addAttribute("error", "请输入用户ID");
            model.addAttribute("hardwareId", LicenseUtils.generateHardwareId());
            return "license/activation";
        }

        if (serialNumber == null || serialNumber.trim().isEmpty()) {
            System.out.println("错误: 序列号为空");
            model.addAttribute("error", "请输入序列号");
            model.addAttribute("hardwareId", LicenseUtils.generateHardwareId());
            model.addAttribute("userId", userId);
            return "license/activation";
        }

        System.out.println("开始执行激活...");
        // 执行激活
        String result = licenseService.activateLicense(userId.trim(), serialNumber.trim());
        System.out.println("激活结果: " + result);

        if ("激活成功".equals(result)) {
            System.out.println("激活成功，准备跳转");
            model.addAttribute("success", "激活成功！正在跳转到登录页面...");
            model.addAttribute("redirectUrl", "/auth/login");
            return "license/activation";
        } else {
            System.out.println("激活失败，返回错误信息");
            model.addAttribute("error", result);
            model.addAttribute("hardwareId", LicenseUtils.generateHardwareId());
            model.addAttribute("userId", userId);
            model.addAttribute("serialNumber", serialNumber);
            return "license/activation";
        }
    }
    
    /**
     * 重新激活（删除现有许可文件）
     */
    @PostMapping("/reactivate")
    public String reactivate() {
        licenseService.deleteLicense();
        return "redirect:/license/activation";
    }
    
    /**
     * 获取许可信息（API接口）
     */
    @GetMapping("/info")
    @ResponseBody
    public Map<String, Object> getLicenseInfo() {
        Map<String, String> licenseInfo = licenseService.getLicenseInfo();
        Map<String, Object> response = new java.util.HashMap<>();
        
        if (licenseInfo != null) {
            response.put("valid", true);
            response.put("info", licenseInfo);
        } else {
            response.put("valid", false);
            response.put("message", "许可无效");
        }
        
        return response;
    }
    
    /**
     * 验证序列号（API接口，用于调试）
     */
    @PostMapping("/validate")
    @ResponseBody
    public Map<String, Object> validateSerialNumber(@RequestParam String userId,
                                                   @RequestParam String serialNumber) {
        Map<String, Object> response = new java.util.HashMap<>();
        
        try {
            boolean valid = LicenseUtils.validateSerialNumber(userId, serialNumber);
            response.put("valid", valid);
            
            if (valid) {
                Map<String, String> info = licenseService.getSerialNumberInfo(serialNumber);
                response.put("info", info);
            }
            
        } catch (Exception e) {
            response.put("valid", false);
            response.put("error", e.getMessage());
        }
        
        return response;
    }
}
