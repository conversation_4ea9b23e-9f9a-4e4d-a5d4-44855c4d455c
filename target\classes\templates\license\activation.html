<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件激活 - 胜大科技智联管理系统</title>
    <link rel="stylesheet" type="text/css" th:href="@{/css/bootstrap.min.css}">
    <link rel="stylesheet" type="text/css" th:href="@{/css/bootstrap-icons.min.css}">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .activation-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 500px;
            width: 100%;
            backdrop-filter: blur(10px);
        }
        
        .logo-section {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo-section h1 {
            color: #333;
            font-weight: 700;
            margin-bottom: 10px;
            font-size: 1.8rem;
        }
        
        .logo-section p {
            color: #666;
            margin-bottom: 0;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-activate {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-size: 16px;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-activate:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            color: white;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .alert-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
        }
        
        .alert-success {
            background: linear-gradient(135deg, #51cf66 0%, #40c057 100%);
            color: white;
        }
        
        .hardware-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }
        
        .hardware-info h6 {
            color: #333;
            margin-bottom: 10px;
            font-weight: 600;
        }
        
        .hardware-info code {
            background: #e9ecef;
            padding: 5px 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            color: #495057;
        }
        
        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .help-text {
            font-size: 14px;
            color: #6c757d;
            margin-top: 5px;
        }
        
        .reactivate-section {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }
        
        .btn-reactivate {
            background: transparent;
            border: 2px solid #6c757d;
            color: #6c757d;
            border-radius: 10px;
            padding: 8px 20px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn-reactivate:hover {
            background: #6c757d;
            color: white;
        }
    </style>
</head>
<body>
    <div class="activation-container">
        <div class="logo-section">
            <h1><i class="bi bi-shield-check"></i> 软件激活</h1>
            <p>胜大科技智联管理系统</p>
        </div>
        
        <!-- 错误信息 -->
        <div th:if="${error}" class="alert alert-danger">
            <i class="bi bi-exclamation-triangle"></i> <span th:text="${error}"></span>
        </div>
        
        <!-- 成功信息 -->
        <div th:if="${success}" class="alert alert-success">
            <i class="bi bi-check-circle"></i> <span th:text="${success}"></span>
        </div>
        
        <!-- 硬件信息 -->
        <div class="hardware-info">
            <h6><i class="bi bi-pc-display"></i> 硬件标识</h6>
            <code th:text="${hardwareId}">HW123456789ABC</code>
            <div class="help-text">此标识用于硬件绑定，请记录备用</div>
        </div>
        
        <!-- 激活表单 -->
        <form th:action="@{/license/activate}" method="post">
            <div class="mb-3">
                <label for="userId" class="form-label">
                    <i class="bi bi-person"></i> 用户ID
                </label>
                <input type="text" 
                       class="form-control" 
                       id="userId" 
                       name="userId" 
                       th:value="${userId}"
                       placeholder="请输入用户ID" 
                       required>
                <div class="help-text">请输入序列号对应的用户ID</div>
            </div>
            
            <div class="mb-4">
                <label for="serialNumber" class="form-label">
                    <i class="bi bi-key"></i> 序列号
                </label>
                <textarea class="form-control"
                          id="serialNumber"
                          name="serialNumber"
                          rows="3"
                          th:text="${serialNumber}"
                          placeholder="请输入序列号"
                          required></textarea>
                <div class="help-text">请输入完整的序列号，格式：SDPLC-XXXXXXXX</div>
            </div>
            
            <button type="submit" class="btn btn-activate">
                <i class="bi bi-unlock"></i> 激活软件
            </button>
        </form>
        
        <!-- 重新激活选项 -->
        <div class="reactivate-section">
            <p class="help-text">如果硬件环境已变更，可以重新激活</p>
            <form th:action="@{/license/reactivate}" method="post" style="display: inline;">
                <button type="submit" class="btn btn-reactivate">
                    <i class="bi bi-arrow-clockwise"></i> 重新激活
                </button>
            </form>
        </div>
    </div>
    
    <script th:src="@{/js/bootstrap.bundle.min.js}"></script>
    <script>
        // 如果激活成功，自动跳转
        if (document.querySelector('.alert-success')) {
            setTimeout(function() {
                window.location.href = '/auth/login';
            }, 2000);
        }
        
        // 用户ID输入格式化（只对用户ID进行大写转换）
        document.getElementById('userId').addEventListener('input', function(e) {
            let value = e.target.value.toUpperCase();
            e.target.value = value;
        });

        // 序列号输入时去除首尾空格
        document.getElementById('serialNumber').addEventListener('blur', function(e) {
            e.target.value = e.target.value.trim();
        });
    </script>
</body>
</html>
