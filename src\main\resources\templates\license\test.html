<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>许可证激活测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .test-container {
            max-width: 1000px;
            margin: 50px auto;
            padding: 20px;
        }
        .test-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 20px;
        }
        .debug-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-test {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-test:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            color: white;
        }
        .info-card {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .alert {
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-card">
            <h2 class="text-center mb-4">
                <i class="fas fa-cogs"></i> 许可证激活测试工具
            </h2>
            
            <!-- 系统信息 -->
            <div class="info-card">
                <h5><i class="fas fa-info-circle"></i> 系统信息</h5>
                <p><strong>硬件ID:</strong> <span th:text="${hardwareId}"></span></p>
                <p><strong>测试时间:</strong> <span id="currentTime"></span></p>
            </div>
            
            <!-- 成功消息 -->
            <div th:if="${success}" class="alert alert-success" role="alert">
                <i class="fas fa-check-circle"></i> <span th:text="${success}"></span>
            </div>
            
            <!-- 错误消息 -->
            <div th:if="${error}" class="alert alert-danger" role="alert">
                <i class="fas fa-exclamation-triangle"></i> <span th:text="${error}"></span>
            </div>
            
            <!-- 测试表单 -->
            <form method="post" action="/license/test-activate">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="userId" class="form-label">用户ID</label>
                            <input type="text" 
                                   class="form-control" 
                                   id="userId" 
                                   name="userId" 
                                   th:value="${testUserId}" 
                                   placeholder="请输入用户ID">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="serialNumber" class="form-label">序列号</label>
                            <textarea class="form-control" 
                                      id="serialNumber" 
                                      name="serialNumber" 
                                      rows="3" 
                                      th:text="${testSerialNumber}" 
                                      placeholder="请输入序列号"></textarea>
                        </div>
                    </div>
                </div>
                
                <div class="text-center">
                    <button type="submit" class="btn btn-test">
                        <i class="fas fa-play"></i> 开始测试激活
                    </button>
                </div>
            </form>
        </div>
        
        <!-- 调试日志 -->
        <div th:if="${debugLog}" class="test-card">
            <h4><i class="fas fa-terminal"></i> 调试日志</h4>
            <div class="debug-log" th:text="${debugLog}"></div>
        </div>
        
        <!-- 快速操作 -->
        <div class="test-card">
            <h4><i class="fas fa-tools"></i> 快速操作</h4>
            <div class="row">
                <div class="col-md-4">
                    <a href="/license/activation" class="btn btn-outline-primary w-100 mb-2">
                        <i class="fas fa-arrow-left"></i> 返回激活页面
                    </a>
                </div>
                <div class="col-md-4">
                    <button onclick="fillTestData()" class="btn btn-outline-success w-100 mb-2">
                        <i class="fas fa-fill"></i> 填充测试数据
                    </button>
                </div>
                <div class="col-md-4">
                    <button onclick="clearForm()" class="btn btn-outline-warning w-100 mb-2">
                        <i class="fas fa-eraser"></i> 清空表单
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script>
        // 显示当前时间
        document.getElementById('currentTime').textContent = new Date().toLocaleString('zh-CN');
        
        // 填充测试数据
        function fillTestData() {
            document.getElementById('userId').value = 'USER001';
            document.getElementById('serialNumber').value = 'SDPLC-zymLUJoCsVDDMwEjeZRTPwu++8QYau8JZURuzUAQiarNtwY88fZFKjKf4DzI1Tqas7ILFaaeJFRiVMrxQ6i8ufGgVDk8NPSyh1VVzAFeyRZFjRW9vSEe19S58Z5tgnjP';
        }
        
        // 清空表单
        function clearForm() {
            document.getElementById('userId').value = '';
            document.getElementById('serialNumber').value = '';
        }
        
        // 自动滚动到调试日志底部
        const debugLog = document.querySelector('.debug-log');
        if (debugLog) {
            debugLog.scrollTop = debugLog.scrollHeight;
        }
    </script>
</body>
</html>
